# Design Document

## Overview

The Abrace Brasil website is a modern, multilingual NextJS application that serves as the digital presence for a Brazilian Portuguese school in Washington DC. The application follows a headless CMS architecture using Strapi as the backend content management system and NextJS with server-side rendering for optimal performance. The design emphasizes cultural authenticity, user experience, and administrative efficiency.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[User Browser] --> B[NextJS Frontend]
    B --> C[Strapi CMS API]
    B --> D[next-intl Translation]
    C --> E[PostgreSQL Database]
    B --> F[External Services]
    F --> G[PayPal Donations]
    F --> H[YouTube Embeds]

    subgraph "NextJS Application"
        B --> I[Server Components]
        B --> J[Client Components]
        B --> K[API Routes]
    end
```

### Technology Stack

- **Frontend Framework**: NextJS 14+ with App Router
- **UI Components**: Shadcn UI with Radix UI primitives
- **Styling**: Tailwind CSS with custom Brazilian-themed color palette
- **Internationalization**: next-intl for client-side translations
- **Calendar Component**: react-big-calendar for event management
- **Content Management**: Strapi CMS with multilingual plugin
- **Database**: PostgreSQL (Strapi backend)
- **Deployment**: Vercel (frontend) + Railway/DigitalOcean (Strapi)

### Folder Structure

```
src/
├── app/
│   ├── [locale]/
│   │   ├── layout.tsx
│   │   ├── page.tsx
│   │   ├── events/
│   │   ├── services/
│   │   ├── blog/
│   │   ├── volunteer/
│   │   └── register/
│   └── api/
├── components/
│   ├── ui/
│   ├── layout/
│   ├── forms/
│   └── calendar/
├── lib/
│   ├── strapi.ts
│   ├── utils.ts
│   └── validations.ts
├── messages/
│   ├── en.json
│   └── pt.json
└── types/
    └── strapi.ts
```

## Components and Interfaces

### Core Components

#### 1. Layout Components

**Header Component**

- Clean white background with subtle shadow
- Logo positioned on the left side
- Horizontal navigation menu with smooth hover effects
- Language switcher (PT/EN) with flag icons
- Donation button with Brazilian-inspired styling
- Mobile-responsive hamburger menu with smooth animations
- Sticky header behavior on scroll

**Hero Section Component**

- Full-width hero area with engaging headline
- Prominent call-to-action buttons with hover animations
- Clean typography hierarchy
- Responsive layout for all screen sizes
- Optional background image or gradient

**Footer Component**

- Multi-column layout with organized information
- Contact information with icons
- Social media links with hover effects
- Quick navigation links
- School mission statement
- Newsletter signup form
- Brazilian cultural elements in design

#### 2. Content Components

**EventCalendar Component**

```typescript
interface EventCalendarProps {
  events: StrapiEvent[];
  locale: string;
  onEventSelect: (event: StrapiEvent) => void;
}
```

**ServiceCard Component**

```typescript
interface ServiceCardProps {
  service: StrapiService;
  category: StrapiCategory;
  locale: string;
}
```

**BlogCard Component**

```typescript
interface BlogCardProps {
  article: StrapiArticle;
  locale: string;
  showExcerpt?: boolean;
}
```

#### 3. Form Components

**RegistrationForm Component**

```typescript
interface RegistrationFormData {
  parentName: string;
  parentEmail: string;
  parentPhone: string;
  childName: string;
  childAge: number;
  childBirthDate: string;
  emergencyContact: string;
  medicalInfo?: string;
  selectedPrograms: string[];
}
```

**AdminExportPanel Component**

```typescript
interface ExportPanelProps {
  onExport: (filters: DateFilter) => void;
  isEditor: boolean;
}
```

### API Integration Layer

#### Strapi Service Layer

```typescript
class StrapiService {
  async getMenuStructure(locale: string): Promise<MenuStructure>;
  async getEvents(locale: string): Promise<StrapiEvent[]>;
  async getServices(locale: string): Promise<StrapiService[]>;
  async getBlogArticles(
    locale: string,
    type: "blog" | "page"
  ): Promise<StrapiArticle[]>;
  async submitRegistration(data: RegistrationFormData): Promise<void>;
  async exportRegistrations(filters: DateFilter): Promise<Blob>;
}
```

## Data Models

### Strapi Content Types

#### Event Content Type

```typescript
interface StrapiEvent {
  id: number;
  title: string;
  description: string;
  startDate: string;
  endDate: string;
  location: string;
  category: string;
  isRecurring: boolean;
  locale: string;
  localizations: StrapiEvent[];
}
```

#### Service Content Type

```typescript
interface StrapiService {
  id: number;
  title: string;
  description: string;
  requirements: string;
  ageGroup: string;
  schedule: string;
  category: StrapiCategory;
  thumbnail: StrapiMedia;
  locale: string;
  localizations: StrapiService[];
}
```

#### Article Content Type

```typescript
interface StrapiArticle {
  id: number;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  thumbnail: StrapiMedia;
  publishedAt: string;
  type: "blog" | "page";
  locale: string;
  localizations: StrapiArticle[];
}
```

#### Registration Content Type

```typescript
interface StrapiRegistration {
  id: number;
  parentName: string;
  parentEmail: string;
  parentPhone: string;
  childName: string;
  childAge: number;
  childBirthDate: string;
  emergencyContact: string;
  medicalInfo?: string;
  selectedPrograms: string[];
  submittedAt: string;
  status: "pending" | "approved" | "rejected";
}
```

### Menu Structure

```typescript
interface MenuStructure {
  mainMenu: MenuItem[];
}

interface MenuItem {
  id: number;
  label: string;
  url: string;
  target: "_self" | "_blank";
  children?: MenuItem[];
  order: number;
}
```

## Error Handling

### Client-Side Error Handling

1. **Form Validation Errors**

   - Real-time validation with Zod schemas
   - Localized error messages using next-intl
   - Visual feedback with Tailwind CSS error states

2. **API Communication Errors**

   - Retry mechanisms for failed requests
   - Fallback content for missing data
   - User-friendly error messages

3. **Translation Fallbacks**
   - Automatic fallback to Portuguese for missing translations
   - Graceful handling of missing content in selected locale

### Server-Side Error Handling

1. **Strapi API Errors**

   - Proper HTTP status code handling
   - Logging for debugging purposes
   - Graceful degradation for unavailable content

2. **Build-Time Errors**
   - Static generation fallbacks
   - ISR (Incremental Static Regeneration) for dynamic content

## Testing Strategy

### Unit Testing

- **Components**: React Testing Library for component behavior
- **Utilities**: Jest for utility functions and API services
- **Forms**: Validation logic and submission handling
- **Internationalization**: Translation key coverage

### Integration Testing

- **API Integration**: Mock Strapi responses for consistent testing
- **Calendar Functionality**: Event display and interaction testing
- **Form Submission**: End-to-end registration flow testing
- **Navigation**: Menu rendering and routing testing

### E2E Testing

- **User Journeys**: Critical paths like registration and event viewing
- **Multilingual Flows**: Language switching and content display
- **Admin Functions**: CSV export and content management
- **Responsive Design**: Cross-device compatibility testing

### Performance Testing

- **Core Web Vitals**: LCP, FID, CLS monitoring
- **Bundle Size**: JavaScript bundle optimization
- **Image Optimization**: NextJS Image component usage
- **SSR Performance**: Server-side rendering speed

## Design System

### Color Palette (Based on Landing Page Design)

```css
:root {
  /* Primary Colors - From Landing Page */
  --primary-green: #22c55e; /* Bright green from design */
  --primary-blue: #3b82f6; /* Blue accent color */
  --primary-yellow: #fbbf24; /* Warm yellow highlights */

  /* Background Colors */
  --bg-primary: #ffffff; /* Clean white background */
  --bg-secondary: #f8fafc; /* Light gray sections */
  --bg-accent: #ecfdf5; /* Light green background */

  /* Text Colors */
  --text-primary: #1f2937; /* Dark gray for main text */
  --text-secondary: #6b7280; /* Medium gray for secondary text */
  --text-accent: #059669; /* Green for highlights */

  /* Brazilian Flag Inspired Accents */
  --brazil-green: #009739;
  --brazil-yellow: #ffdf00;
  --brazil-blue: #002776;
}
```

### Landing Page Layout Structure

Based on the provided design, the landing page follows this structure:

#### Header Section

- Clean white background with navigation menu
- Logo placement on the left
- Horizontal navigation menu with items like "Início", "Sobre", "Serviços", etc.
- Language switcher (PT/EN) in the top right
- Mobile-responsive hamburger menu for smaller screens

#### Hero Section

- Large hero area with compelling headline
- Prominent call-to-action buttons
- Clean, modern layout with ample white space
- Engaging visual elements to capture attention

#### Content Sections

- Card-based layout for services and programs
- Clean grid system for organizing information
- Consistent spacing and typography hierarchy
- Visual icons and imagery to support content

#### Visual Design Elements

- Rounded corners on cards and buttons
- Subtle shadows for depth
- Consistent color usage throughout
- Modern, clean aesthetic with Brazilian cultural touches

### Typography

- **Primary Font**: Inter (clean, modern, multilingual support)
- **Accent Font**: Poppins (for headings and emphasis)
- **Font Sizes**: Tailwind CSS scale with custom Brazilian-inspired adjustments
- **Hierarchy**: Clear distinction between headings, subheadings, and body text
- **Line Height**: Optimized for readability in both Portuguese and English

### Animation Guidelines

- **Micro-interactions**: Subtle hover effects, button states
- **Page Transitions**: Smooth navigation between sections
- **Loading States**: Skeleton screens and progress indicators
- **Calendar Animations**: Smooth month transitions and event highlighting

### Responsive Breakpoints

```css
/* Tailwind CSS breakpoints */
sm: 640px   /* Mobile landscape */
md: 768px   /* Tablet */
lg: 1024px  /* Desktop */
xl: 1280px  /* Large desktop */
2xl: 1536px /* Extra large desktop */
```

## Security Considerations

### Data Protection

- **Form Validation**: Server-side validation for all user inputs
- **CSRF Protection**: NextJS built-in CSRF protection
- **Rate Limiting**: API route protection against abuse
- **Data Sanitization**: XSS prevention for user-generated content

### Authentication & Authorization

- **Admin Access**: Strapi role-based permissions for editors
- **API Security**: Proper API key management for Strapi integration
- **Session Management**: Secure handling of admin sessions

### Content Security

- **Image Uploads**: Validation and sanitization through Strapi
- **YouTube Embeds**: Secure iframe implementation
- **External Links**: Proper rel attributes for security

## Performance Optimization

### NextJS Optimizations

- **Static Generation**: Pre-render static pages at build time
- **ISR**: Incremental Static Regeneration for dynamic content
- **Image Optimization**: NextJS Image component with proper sizing
- **Bundle Splitting**: Automatic code splitting for optimal loading

### Caching Strategy

- **Static Assets**: Long-term caching for images and fonts
- **API Responses**: Appropriate cache headers for Strapi content
- **CDN Integration**: Vercel Edge Network for global performance

### Loading Performance

- **Critical CSS**: Inline critical styles for above-the-fold content
- **Lazy Loading**: Images and non-critical components
- **Preloading**: Strategic resource preloading for key pages
