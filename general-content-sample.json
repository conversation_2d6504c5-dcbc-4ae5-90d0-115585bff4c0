//FETCH URL: localhost:1337/api/general-content?populate=*

{
  "data": {
    "id": 7,
    "documentId": "tkvbxd17lk0wkr42auo254o2",
    "phone": null,
    "email": "<EMAIL>",
    "address": "Langley High School — 6520 Georgetown Pike, McLean, VA 22101\nP.O. Box 813, McLean, VA 22101",
    "facebook": "https://www.facebook.com/ABRACEBrasil.org/",
    "instagram": "https://www.instagram.com/abracebrasil/",
    "linkedin": null,
    "youtube": null,
    "tiktok": null,
    "whatsapp": null,
    "donationlink": "https://www.paypal.com/donate?token=ydyB2Dg1kKmNwDcmWXKWu_cDljn7qqXGFM9A6LegcBYBmMkp1wAFGr1s6ADsFss0o-ajt3JdImCsvfUM",
    "createdAt": "2025-09-21T18:53:12.920Z",
    "updatedAt": "2025-09-22T01:30:58.371Z",
    "publishedAt": "2025-09-22T01:30:58.390Z",
    "volunteerlink": "https://docs.google.com/forms/d/e/1FAIpQLScyEsZJFvtIKQluvvFxeIIvEqCYKCrDwWNpVYudV3w3YZdu-Q/viewform",
    "enrollmentlink": null,
    "careerlink": null,
    "aboutUs": [
      {
        "type": "heading",
        "level": 2,
        "children": [
          {
            "text": "Um Pouco Sobre Nós",
            "type": "text"
          }
        ]
      },
      {
        "type": "paragraph",
        "children": [
          {
            "text": "Podemos estar longe, mas não deixamos de abraçar nossa origem.",
            "type": "text"
          }
        ]
      },
      {
        "type": "paragraph",
        "children": [
          {
            "text": "A Abrace Brasil nasceu do desejo de manter viva a cultura brasileira entre as famílias que vivem em Washington DC. Acreditamos que a língua e as tradições são pontes que conectam nossos filhos às suas raízes, mesmo estando longe do Brasil.",
            "type": "text"
          }
        ]
      },
      {
        "type": "paragraph",
        "children": [
          {
            "text": "Nossa missão é criar um ambiente acolhedor onde crianças e adultos possam aprender português, celebrar nossa cultura e formar uma comunidade brasileira forte e unida.",
            "type": "text"
          }
        ]
      }
    ],
    "aboutUsVideoLink": "https://www.youtube.com/watch?v=JAbw3ydVMvQ",
    "logo": {
      "id": 1,
      "documentId": "tfyctcdqjcb9cclwcq5gmu3m",
      "name": "abrace-brasil-logo.jpg",
      "alternativeText": null,
      "caption": null,
      "width": 303,
      "height": 250,
      "formats": {
        "thumbnail": {
          "ext": ".jpg",
          "url": "https://abracebrasil-strapi.s3.us-east-2.amazonaws.com/uploads/thumbnail_abrace_brasil_logo_d0443ed8f8.jpg",
          "hash": "thumbnail_abrace_brasil_logo_d0443ed8f8",
          "mime": "image/jpeg",
          "name": "thumbnail_abrace-brasil-logo.jpg",
          "path": null,
          "size": 9.17,
          "width": 189,
          "height": 156,
          "sizeInBytes": 9168
        }
      },
      "hash": "abrace_brasil_logo_d0443ed8f8",
      "ext": ".jpg",
      "mime": "image/jpeg",
      "size": 17.89,
      "url": "https://abracebrasil-strapi.s3.us-east-2.amazonaws.com/uploads/abrace_brasil_logo_d0443ed8f8.jpg",
      "previewUrl": null,
      "provider": "aws-s3",
      "provider_metadata": null,
      "createdAt": "2025-09-21T20:49:39.962Z",
      "updatedAt": "2025-09-21T22:04:10.518Z",
      "publishedAt": "2025-09-21T20:49:39.962Z"
    },
    "logodark": null,
    "logomobile": null,
    "logomobiledark": null,
    "badges": [
      {
        "id": 10,
        "title": "Great Non Profits 2013"
      },
      {
        "id": 11,
        "title": "Great Non Profits 2016"
      },
      {
        "id": 12,
        "title": "Great Non Profits 2024"
      }
    ]
  },
  "meta": {}
}
