{"name": "abrace-brasil-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@tailwindcss/typography": "^0.5.18", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.539.0", "moment": "^2.30.1", "next": "15.4.6", "next-intl": "^4.3.4", "react": "19.1.0", "react-big-calendar": "^1.19.4", "react-day-picker": "^9.8.1", "react-dom": "19.1.0", "swiper": "^11.2.10", "tailwind-merge": "^3.3.1", "zod": "^4.0.17"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/moment": "^2.11.29", "@types/node": "^20", "@types/react": "^19", "@types/react-big-calendar": "^1.16.2", "@types/react-dom": "^19", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}