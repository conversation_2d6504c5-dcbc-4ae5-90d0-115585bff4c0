import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { RichText } from "@/components/ui/rich-text";
import { strapiService } from "@/lib/strapi";
import { StrapiPage } from "@/types/strapi";
import { useEffect, useState } from "react";

export default function AboutPage() {
  const [aboutPage, setAboutPage] = useState<StrapiPage | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchAboutPage = async () => {
      try {
        const pages = await strapiService.getPages();
        const about = pages.find(page => page.category === 'about');
        setAboutPage(about || null);
      } catch (error) {
        console.error('Error fetching about page:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAboutPage();
  }, []);

  return (
    <div className="min-h-screen">
      <Header />
      
      <main className="py-16">
        <div className="container mx-auto px-4">
          {loading ? (
            <div className="max-w-4xl mx-auto">
              <div className="h-12 bg-gray-200 animate-pulse rounded mb-8"></div>
              <div className="space-y-4">
                <div className="h-4 bg-gray-200 animate-pulse rounded"></div>
                <div className="h-4 bg-gray-200 animate-pulse rounded"></div>
                <div className="h-4 bg-gray-200 animate-pulse rounded w-3/4"></div>
              </div>
            </div>
          ) : aboutPage ? (
            <div className="max-w-4xl mx-auto">
              <h1 className="text-4xl font-bold text-gray-900 mb-8">
                {aboutPage.title}
              </h1>
              <div className="prose prose-lg max-w-none">
                <RichText content={aboutPage.content} />
              </div>
            </div>
          ) : (
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl font-bold text-gray-900 mb-8">
                Sobre Nós
              </h1>
              <div className="prose prose-lg max-w-none">
                <p>
                  A Abrace Brasil nasceu do desejo de manter viva a cultura
                  brasileira entre as famílias que vivem em Washington DC.
                  Acreditamos que a língua e as tradições são pontes que conectam
                  nossos filhos às suas raízes, mesmo estando longe do Brasil.
                </p>
                <p>
                  Nossa missão é criar um ambiente acolhedor onde crianças e
                  adultos possam aprender português, celebrar nossa cultura e
                  formar uma comunidade brasileira forte e unida.
                </p>
              </div>
            </div>
          )}
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
