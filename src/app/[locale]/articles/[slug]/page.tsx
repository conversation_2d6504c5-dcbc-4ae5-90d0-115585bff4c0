import { strapiService } from "@/lib/strapi";
import { getTranslations } from "next-intl/server";
import { notFound } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { Calendar, ArrowLeft } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { RichContent } from "@/components/blog/rich-content";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";

interface ArticlePageProps {
  params: Promise<{
    locale: string;
    slug: string;
  }>;
}

export async function generateMetadata({ params }: ArticlePageProps) {
  const { locale, slug } = await params;
  const article = await strapiService.getArticleBySlug(slug, locale);

  if (!article) {
    return {
      title: "Article Not Found",
    };
  }

  return {
    title: article.title,
    description: article.excerpt,
    openGraph: {
      title: article.title,
      description: article.excerpt,
      images: [
        {
          url: article.thumbnail.url,
          width: article.thumbnail.width,
          height: article.thumbnail.height,
          alt: article.thumbnail.alternativeText || article.title,
        },
      ],
    },
  };
}

export default async function ArticlePage({ params }: ArticlePageProps) {
  const { locale, slug } = await params;
  const article = await strapiService.getArticleBySlug(slug, locale);
  const t = await getTranslations({ locale, namespace: "blog" });

  if (!article) {
    notFound();
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(locale === "pt-BR" ? "pt-BR" : "en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Back to Blog Button */}
      <div className="container mx-auto px-4 py-6">
        <Button asChild variant="ghost" className="mb-4">
          <Link href={`/${locale}/blog`} className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            {t("back_to_blog")}
          </Link>
        </Button>
      </div>

      {/* Article Header */}
      <article className="container mx-auto px-4 pb-16">
        <header className="mb-8">
          <div className="relative aspect-video mb-8 overflow-hidden rounded-lg">
            <Image
              src={article.thumbnail.url}
              alt={article.thumbnail.alternativeText || article.title}
              fill
              className="object-cover"
              priority
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1200px"
            />
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-4">
              <Calendar className="h-4 w-4" />
              <span>
                {t("published_on")} {formatDate(article.publishedAt)}
              </span>
            </div>

            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              {article.title}
            </h1>

            {article.excerpt && (
              <p className="text-xl text-gray-600 leading-relaxed mb-8">
                {article.excerpt}
              </p>
            )}
          </div>
        </header>

        {/* Article Content */}
        <div className="max-w-4xl mx-auto">
          <RichContent content={article.content} />
        </div>

        {/* Article Footer */}
        <footer className="max-w-4xl mx-auto mt-12 pt-8 border-t border-gray-200">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <Button asChild variant="outline">
              <Link
                href={`/${locale}/blog`}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                {t("back_to_blog")}
              </Link>
            </Button>

            <div className="text-sm text-muted-foreground">
              {t("published_on")} {formatDate(article.publishedAt)}
            </div>
          </div>
        </footer>
      </article>

      <Footer />
    </div>
  );
}
