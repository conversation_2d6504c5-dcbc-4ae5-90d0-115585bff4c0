import { notFound } from "next/navigation";
import { strapiService } from "@/lib/strapi";
import { RichText } from "@/components/ui/rich-text";
import Image from "next/image";

interface EventPageProps {
  params: {
    slug: string;
    locale: string;
  };
}

export default async function EventPage({ params }: EventPageProps) {
  const { slug, locale } = params;
  
  try {
    const events = await strapiService.getCalendars();
    const event = events.find(e => e.slug === slug);
    
    if (!event) {
      notFound();
    }

    return (
      <div className="min-h-screen bg-white">
        <div className="container mx-auto px-4 py-12">
          <div className="max-w-4xl mx-auto">
            {/* Header */}
            <div className="mb-8">
              {event.thumbnail && (
                <div className="mb-6">
                  <Image
                    src={event.thumbnail.url}
                    alt={event.thumbnail.alternativeText || event.title}
                    width={event.thumbnail.width}
                    height={event.thumbnail.height}
                    className="w-full h-64 object-cover rounded-lg"
                  />
                </div>
              )}
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                {event.title}
              </h1>
              <div className="flex flex-wrap gap-4 text-sm text-gray-600 mb-4">
                <div className="flex items-center">
                  <span className="font-semibold">Data:</span>
                  <span className="ml-2">
                    {new Date(event.startDate).toLocaleDateString('pt-BR')}
                  </span>
                </div>
                <div className="flex items-center">
                  <span className="font-semibold">Horário:</span>
                  <span className="ml-2">
                    {event.startTime} - {event.endTime}
                  </span>
                </div>
                {event.location && (
                  <div className="flex items-center">
                    <span className="font-semibold">Local:</span>
                    <span className="ml-2">{event.location}</span>
                  </div>
                )}
                {event.category && (
                  <div className="flex items-center">
                    <span className="font-semibold">Categoria:</span>
                    <span className="ml-2">{event.category}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Content */}
            <div className="prose prose-lg max-w-none">
              <RichText content={event.content} />
            </div>

            {/* Action Buttons */}
            <div className="mt-8 flex gap-4">
              <a
                href="/volunteer"
                className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Voluntarie-se
              </a>
              <a
                href="/register"
                className="inline-block bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
              >
                Matrículas
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error fetching event:', error);
    notFound();
  }
}
