import { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import { EventsPageClient } from "@/components/events/events-page-client";
import { strapiService } from "@/lib/strapi";

interface EventsPageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({
  params,
}: EventsPageProps): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "events" });

  return {
    title: t("title"),
    description: t("description"),
  };
}

export default async function EventsPage({ params }: EventsPageProps) {
  const { locale } = await params;

  // Fetch events from Strapi
  const events = await strapiService.getEvents(locale);

  return (
    <div className="min-h-screen bg-white">
      <EventsPageClient events={events} locale={locale} />
    </div>
  );
}
