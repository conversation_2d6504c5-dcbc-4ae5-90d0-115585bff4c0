import { notFound } from "next/navigation";
import { strapiService } from "@/lib/strapi";
import { RichText } from "@/components/ui/rich-text";
import Image from "next/image";

interface ProgramPageProps {
  params: {
    slug: string;
    locale: string;
  };
}

export default async function ProgramPage({ params }: ProgramPageProps) {
  const { slug, locale } = params;
  
  try {
    const programs = await strapiService.getPrograms();
    const program = programs.find(p => p.slug === slug);
    
    if (!program) {
      notFound();
    }

    return (
      <div className="min-h-screen bg-white">
        <div className="container mx-auto px-4 py-12">
          <div className="max-w-4xl mx-auto">
            {/* Header */}
            <div className="mb-8">
              {program.thumbnail && (
                <div className="mb-6">
                  <Image
                    src={program.thumbnail.url}
                    alt={program.thumbnail.alternativeText || program.title}
                    width={program.thumbnail.width}
                    height={program.thumbnail.height}
                    className="w-full h-64 object-cover rounded-lg"
                  />
                </div>
              )}
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                {program.title}
              </h1>
            </div>

            {/* Content */}
            <div className="prose prose-lg max-w-none">
              <RichText content={program.content} />
            </div>

            {/* Action Buttons */}
            {program.linkbutton && program.linkbutton.length > 0 && (
              <div className="mt-8 flex gap-4">
                {program.linkbutton.map((button, index) => (
                  <a
                    key={index}
                    href={button.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    {button.label}
                  </a>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error fetching program:', error);
    notFound();
  }
}
