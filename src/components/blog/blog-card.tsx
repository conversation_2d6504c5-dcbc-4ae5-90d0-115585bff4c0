"use client";

import { StrapiArticle } from "@/types/strapi";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { Calendar } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface BlogCardProps {
  article: StrapiArticle;
  locale: string;
  showExcerpt?: boolean;
}

export function BlogCard({
  article,
  locale,
  showExcerpt = true,
}: BlogCardProps) {
  const t = useTranslations("blog");

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(locale === "pt-BR" ? "pt-BR" : "en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <Card className="h-full flex flex-col overflow-hidden hover:shadow-lg transition-shadow duration-300">
      <div className="relative aspect-video overflow-hidden">
        <Image
          src={article.thumbnail.url}
          alt={article.thumbnail.alternativeText || article.title}
          fill
          className="object-cover transition-transform duration-300 hover:scale-105"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
      </div>

      <CardHeader className="flex-grow">
        <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
          <Calendar className="h-4 w-4" />
          <span>{formatDate(article.publishedAt)}</span>
        </div>
        <h3 className="text-xl font-semibold line-clamp-2 hover:text-primary transition-colors">
          <Link href={`/${locale}/blog/${article.slug}`}>{article.title}</Link>
        </h3>
      </CardHeader>

      {showExcerpt && (
        <CardContent className="flex-grow">
          <p className="text-muted-foreground line-clamp-3">
            {article.excerpt}
          </p>
        </CardContent>
      )}

      <CardFooter>
        <Button asChild variant="outline" className="w-full">
          <Link href={`/${locale}/blog/${article.slug}`}>{t("read_more")}</Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
