"use client";

import { useEffect, useRef } from "react";

interface RichContentProps {
  content: string;
}

export function RichContent({ content }: RichContentProps) {
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!contentRef.current) return;

    // Process YouTube iframes to make them responsive
    const iframes = contentRef.current.querySelectorAll(
      'iframe[src*="youtube.com"], iframe[src*="youtu.be"]'
    );

    iframes.forEach((iframe) => {
      const wrapper = document.createElement("div");
      wrapper.className =
        "relative w-full aspect-video my-8 rounded-lg overflow-hidden shadow-lg";

      // Clone the iframe with responsive classes
      const newIframe = iframe.cloneNode(true) as HTMLIFrameElement;
      newIframe.className = "absolute inset-0 w-full h-full";
      newIframe.removeAttribute("width");
      newIframe.removeAttribute("height");

      // Replace the original iframe with the wrapped version
      iframe.parentNode?.replaceChild(wrapper, iframe);
      wrapper.appendChild(newIframe);
    });

    // Style other elements
    const headings = contentRef.current.querySelectorAll(
      "h1, h2, h3, h4, h5, h6"
    );
    headings.forEach((heading) => {
      heading.classList.add("font-bold", "text-gray-900", "mb-4", "mt-8");

      if (heading.tagName === "H1") {
        heading.classList.add("text-3xl", "md:text-4xl");
      } else if (heading.tagName === "H2") {
        heading.classList.add("text-2xl", "md:text-3xl");
      } else if (heading.tagName === "H3") {
        heading.classList.add("text-xl", "md:text-2xl");
      } else {
        heading.classList.add("text-lg", "md:text-xl");
      }
    });

    const paragraphs = contentRef.current.querySelectorAll("p");
    paragraphs.forEach((p) => {
      p.classList.add("text-gray-700", "leading-relaxed", "mb-6", "text-lg");
    });

    const lists = contentRef.current.querySelectorAll("ul, ol");
    lists.forEach((list) => {
      list.classList.add("text-gray-700", "leading-relaxed", "mb-6", "text-lg");
      if (list.tagName === "UL") {
        list.classList.add("list-disc", "list-inside", "space-y-2");
      } else {
        list.classList.add("list-decimal", "list-inside", "space-y-2");
      }
    });

    const listItems = contentRef.current.querySelectorAll("li");
    listItems.forEach((li) => {
      li.classList.add("ml-4");
    });

    const links = contentRef.current.querySelectorAll("a");
    links.forEach((link) => {
      link.classList.add(
        "text-primary",
        "hover:text-primary/80",
        "underline",
        "transition-colors"
      );
    });

    const blockquotes = contentRef.current.querySelectorAll("blockquote");
    blockquotes.forEach((blockquote) => {
      blockquote.classList.add(
        "border-l-4",
        "border-primary",
        "pl-6",
        "py-4",
        "my-8",
        "bg-gray-50",
        "italic",
        "text-gray-700",
        "rounded-r-lg"
      );
    });

    const images = contentRef.current.querySelectorAll("img");
    images.forEach((img) => {
      img.classList.add(
        "rounded-lg",
        "shadow-md",
        "my-8",
        "max-w-full",
        "h-auto"
      );

      // Wrap images in a figure if they have alt text
      if (img.alt) {
        const figure = document.createElement("figure");
        figure.className = "my-8";

        const figcaption = document.createElement("figcaption");
        figcaption.className = "text-sm text-gray-500 text-center mt-2 italic";
        figcaption.textContent = img.alt;

        img.parentNode?.replaceChild(figure, img);
        figure.appendChild(img);
        figure.appendChild(figcaption);
      }
    });

    const codeBlocks = contentRef.current.querySelectorAll("pre");
    codeBlocks.forEach((pre) => {
      pre.classList.add(
        "bg-gray-900",
        "text-gray-100",
        "p-4",
        "rounded-lg",
        "overflow-x-auto",
        "my-6",
        "text-sm"
      );
    });

    const inlineCode = contentRef.current.querySelectorAll("code");
    inlineCode.forEach((code) => {
      if (!code.parentElement?.tagName.includes("PRE")) {
        code.classList.add(
          "bg-gray-100",
          "text-gray-800",
          "px-2",
          "py-1",
          "rounded",
          "text-sm",
          "font-mono"
        );
      }
    });
  }, [content]);

  return (
    <div
      ref={contentRef}
      className="prose prose-lg max-w-none"
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
}
