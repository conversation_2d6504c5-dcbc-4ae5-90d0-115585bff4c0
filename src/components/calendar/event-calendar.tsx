"use client";

import { Calendar, momentLocalizer, View } from "react-big-calendar";
import moment from "moment";
import "moment/locale/pt-br";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";

// Set moment locale to Portuguese Brazil
moment.locale("pt-br");
const localizer = momentLocalizer(moment);

interface CalendarEvent {
  id: number;
  title: string;
  start: Date;
  end: Date;
  resource?: any;
}

interface EventCalendarProps {
  events?: CalendarEvent[];
  onSelectEvent?: (event: CalendarEvent) => void;
  onSelectSlot?: (slotInfo: any) => void;
}

export function EventCalendar({
  events = [],
  onSelectEvent,
  onSelectSlot,
}: EventCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [currentView, setCurrentView] = useState<View>("month");

  // Sample events for demonstration
  const sampleEvents: CalendarEvent[] = [
    {
      id: 1,
      title: "Aula de Capoeira",
      start: new Date(2025, 0, 15, 10, 0), // January 15, 2025, 10:00 AM
      end: new Date(2025, 0, 15, 11, 30), // January 15, 2025, 11:30 AM
    },
    {
      id: 2,
      title: "Festa Junina",
      start: new Date(2025, 0, 22, 14, 0), // January 22, 2025, 2:00 PM
      end: new Date(2025, 0, 22, 18, 0), // January 22, 2025, 6:00 PM
    },
    {
      id: 3,
      title: "Aula de Português",
      start: new Date(2025, 0, 28, 9, 0), // January 28, 2025, 9:00 AM
      end: new Date(2025, 0, 28, 10, 30), // January 28, 2025, 10:30 AM
    },
  ];

  const allEvents = events.length > 0 ? events : sampleEvents;
  
  console.log('Calendar events:', allEvents);

  const handleNavigate = (newDate: Date) => {
    setCurrentDate(newDate);
  };

  const handleViewChange = (newView: View) => {
    setCurrentView(newView);
  };

  // Custom toolbar component
  const CustomToolbar = () => {
    const goToBack = () => {
      const newDate = new Date(
        currentDate.getFullYear(),
        currentDate.getMonth() - 1,
        1
      );
      setCurrentDate(newDate);
    };

    const goToNext = () => {
      const newDate = new Date(
        currentDate.getFullYear(),
        currentDate.getMonth() + 1,
        1
      );
      setCurrentDate(newDate);
    };

    const goToCurrent = () => {
      setCurrentDate(new Date());
    };

    const monthNames = [
      "Janeiro",
      "Fevereiro",
      "Março",
      "Abril",
      "Maio",
      "Junho",
      "Julho",
      "Agosto",
      "Setembro",
      "Outubro",
      "Novembro",
      "Dezembro",
    ];

    // Ensure we have a valid date object
    const safeDate = currentDate instanceof Date ? currentDate : new Date();

    return (
      <div className="flex flex-col sm:flex-row items-center justify-between mb-6 gap-4">
        <div className="flex items-center space-x-4">
          <select
            value={monthNames[safeDate.getMonth()]}
            onChange={(e) => {
              const monthIndex = monthNames.indexOf(e.target.value);
              const newDate = new Date(safeDate.getFullYear(), monthIndex, 1);
              setCurrentDate(newDate);
            }}
            className="px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-blue"
          >
            {monthNames.map((month, index) => (
              <option key={index} value={month}>
                {month}
              </option>
            ))}
          </select>

          <select
            value={safeDate.getFullYear()}
            onChange={(e) => {
              const newDate = new Date(
                parseInt(e.target.value),
                safeDate.getMonth(),
                1
              );
              setCurrentDate(newDate);
            }}
            className="px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-blue"
          >
            {Array.from({ length: 10 }, (_, i) => 2020 + i).map((year) => (
              <option key={year} value={year}>
                {year}
              </option>
            ))}
          </select>

          <Button
            variant="outline"
            size="sm"
            onClick={goToCurrent}
            className="border-primary-blue text-primary-blue hover:bg-primary-blue hover:text-white"
          >
            Hoje
          </Button>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={goToBack}
            className="p-2"
          >
            <ChevronLeft size={16} />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={goToNext}
            className="p-2"
          >
            <ChevronRight size={16} />
          </Button>
        </div>
      </div>
    );
  };

  // Custom event component
  const EventComponent = ({ event }: { event: CalendarEvent }) => (
    <div className="text-xs font-medium truncate">{event.title}</div>
  );

  return (
    <div className="w-full">
      <CustomToolbar />

      <Calendar
        localizer={localizer}
        events={allEvents}
        startAccessor="start"
        endAccessor="end"
        style={{ height: 500 }}
        onSelectEvent={onSelectEvent}
        onSelectSlot={onSelectSlot}
        selectable
        date={currentDate}
        onNavigate={handleNavigate}
        view={currentView}
        onView={handleViewChange}
        components={{
          toolbar: () => null, // Hide default toolbar since we have custom one
          event: EventComponent,
        }}
        messages={{
          next: "Próximo",
          previous: "Anterior",
          today: "Hoje",
          month: "Mês",
          week: "Semana",
          day: "Dia",
          agenda: "Agenda",
          date: "Data",
          time: "Hora",
          event: "Evento",
          noEventsInRange: "Não há eventos neste período.",
          showMore: (total) => `+ Ver mais (${total})`,
        }}
      />
    </div>
  );
}
