"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { EventCalendar } from "@/components/calendar/event-calendar";
import { EventDetailModal } from "@/components/events/event-detail-modal";
import { StrapiEvent } from "@/types/strapi";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";

interface EventsPageClientProps {
  events: StrapiEvent[];
  locale: string;
}

// Convert StrapiEvent to Calendar Event format
const convertToCalendarEvent = (strapiEvent: StrapiEvent) => ({
  id: strapiEvent.id,
  title: strapiEvent.title,
  start: new Date(strapiEvent.startDate),
  end: new Date(strapiEvent.endDate),
  resource: strapiEvent,
});

export function EventsPageClient({ events, locale }: EventsPageClientProps) {
  const t = useTranslations("events");
  const [selectedEvent, setSelectedEvent] = useState<StrapiEvent | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Convert Strapi events to calendar format
  const calendarEvents = events.map(convertToCalendarEvent);

  const handleEventSelect = (event: any) => {
    setSelectedEvent(event.resource);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setSelectedEvent(null);
    setIsModalOpen(false);
  };

  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <section className="bg-blue-600 text-white py-8">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl md:text-3xl font-bold font-poppins mb-4">
                {t("title")}
              </h1>
              <p className="text-xl md:text-xl opacity-90">{t("subtitle")}</p>
            </div>
          </div>
        </section>

        {/* Calendar Section */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <div className="mb-8">
                <h2 className="text-2xl md:text-3xl font-bold font-poppins text-gray-900 mb-4">
                  {t("calendar_title")}
                </h2>
                <p className="text-gray-600 text-lg">
                  {t("calendar_description")}
                </p>
              </div>

              <EventCalendar
                events={calendarEvents}
                onSelectEvent={handleEventSelect}
              />
            </div>
          </div>
        </section>

        {/* Upcoming Events Section */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-2xl md:text-3xl font-bold font-poppins text-gray-900 mb-8 text-center">
                {t("upcoming_events")}
              </h2>

              {events.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-gray-600 text-lg">{t("no_events")}</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {events
                    .filter((event) => new Date(event.startDate) >= new Date())
                    .sort(
                      (a, b) =>
                        new Date(a.startDate).getTime() -
                        new Date(b.startDate).getTime()
                    )
                    .slice(0, 6)
                    .map((event) => (
                      <EventCard
                        key={event.id}
                        event={event}
                        locale={locale}
                        onClick={() => {
                          setSelectedEvent(event);
                          setIsModalOpen(true);
                        }}
                      />
                    ))}
                </div>
              )}
            </div>
          </div>
        </section>
      </main>
      <Footer />

      {/* Event Detail Modal */}
      {selectedEvent && (
        <EventDetailModal
          event={selectedEvent}
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          locale={locale}
        />
      )}
    </>
  );
}

// Event Card Component
interface EventCardProps {
  event: StrapiEvent;
  locale: string;
  onClick: () => void;
}

function EventCard({ event, locale, onClick }: EventCardProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(locale === "pt-BR" ? "pt-BR" : "en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString(locale === "pt-BR" ? "pt-BR" : "en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div
      className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 cursor-pointer overflow-hidden"
      onClick={onClick}
    >
      <div className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h3 className="text-xl font-semibold font-poppins text-gray-900 mb-2">
              {event.title}
            </h3>
            <span className="inline-block px-3 py-1 bg-primary-green/10 text-primary-green text-sm font-medium rounded-full">
              {event.category}
            </span>
          </div>
        </div>

        <div className="space-y-2 text-gray-600">
          <div className="flex items-center">
            <svg
              className="w-4 h-4 mr-2"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                clipRule="evenodd"
              />
            </svg>
            <span className="text-sm">{formatDate(event.startDate)}</span>
          </div>
          <div className="flex items-center">
            <svg
              className="w-4 h-4 mr-2"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                clipRule="evenodd"
              />
            </svg>
            <span className="text-sm">
              {formatTime(event.startDate)} - {formatTime(event.endDate)}
            </span>
          </div>
          {event.location && (
            <div className="flex items-center">
              <svg
                className="w-4 h-4 mr-2"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                  clipRule="evenodd"
                />
              </svg>
              <span className="text-sm">{event.location}</span>
            </div>
          )}
        </div>

        <p className="text-gray-700 mt-4 line-clamp-3">{event.description}</p>
      </div>
    </div>
  );
}
