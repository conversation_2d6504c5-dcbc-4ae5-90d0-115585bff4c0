"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { RichText } from "@/components/ui/rich-text";
import { strapiService } from "@/lib/strapi";
import { StrapiGeneralContent } from "@/types/strapi";
import { useEffect, useState } from "react";

export function AboutSection() {
  const [generalContent, setGeneralContent] = useState<StrapiGeneralContent | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const content = await strapiService.getGeneralContent();
        setGeneralContent(content);
      } catch (error) {
        console.error('Error fetching general content:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-12 items-center bg-blue-600 rounded-xl p-16">
          {/* Left Content */}
          <div className="space-y-6">
            <h2 className="text-3xl lg:text-4xl font-bold text-white">
              Um Pouco Sobre Nós
            </h2>

            <div className="w-full">
              {loading ? (
                <div className="space-y-4">
                  <div className="h-6 bg-white/20 animate-pulse rounded"></div>
                  <div className="h-4 bg-white/20 animate-pulse rounded"></div>
                  <div className="h-4 bg-white/20 animate-pulse rounded"></div>
                  <div className="h-4 bg-white/20 animate-pulse rounded w-3/4"></div>
                  <div className="h-4 bg-white/20 animate-pulse rounded"></div>
                  <div className="h-4 bg-white/20 animate-pulse rounded w-2/3"></div>
                </div>
              ) : (
                <>
                  <p className="text-lg text-white leading-relaxed mb-4">
                    <strong>
                      Podemos estar longe, mas não deixamos de abraçar nossa origem.
                    </strong>
                  </p>
                  <p className="text-white leading-relaxed mb-6">
                    A Abrace Brasil nasceu do desejo de manter viva a cultura
                    brasileira entre as famílias que vivem em Washington DC.
                    Acreditamos que a língua e as tradições são pontes que conectam
                    nossos filhos às suas raízes, mesmo estando longe do Brasil.
                  </p>
                  <p className="text-white leading-relaxed mb-6">
                    Nossa missão é criar um ambiente acolhedor onde crianças e
                    adultos possam aprender português, celebrar nossa cultura e
                    formar uma comunidade brasileira forte e unida.
                  </p>
                  <Button className="bg-yellow-400 text-green-900 hover:bg-yellow-500" asChild>
                    <a href="/about">Saiba mais</a>
                  </Button>
                </>
              )}
            </div>
          </div>

          {/* Right Content - Image/Video Placeholder */}
          <div className="relative">
            {loading ? (
              <div className="aspect-video bg-gray-200 animate-pulse rounded-2xl"></div>
            ) : (
              <div className="aspect-video bg-gray-900 rounded-2xl overflow-hidden relative">
                {/* Video placeholder */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center space-y-4">
                    <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto">
                      <div className="w-0 h-0 border-l-[12px] border-l-white border-y-[8px] border-y-transparent ml-1"></div>
                    </div>
                    <p className="text-white font-medium">
                      Assista nosso vídeo institucional
                    </p>
                  </div>
                </div>

                {/* Overlay gradient */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
