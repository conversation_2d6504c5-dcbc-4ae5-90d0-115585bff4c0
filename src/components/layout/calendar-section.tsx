"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { EventCalendar } from "@/components/calendar/event-calendar";
import { strapiService } from "@/lib/strapi";
import { StrapiEvent } from "@/types/strapi";
import { useEffect, useState } from "react";

export function CalendarSection() {
  const [events, setEvents] = useState<StrapiEvent[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        const data = await strapiService.getCalendars();
        console.log('Fetched events:', data);
        setEvents(data);
      } catch (error) {
        console.error('Error fetching events:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, []);

  const handleEventSelect = (event: any) => {
    console.log("Event selected:", event);
    // Navigate to event detail page
    if (event.resource && event.resource.slug) {
      window.location.href = `/events/${event.resource.slug}`;
    }
  };

  const handleSlotSelect = (slotInfo: any) => {
    console.log("Slot selected:", slotInfo);
    // Handle slot selection - could open a form to create new event
  };

  return (
    <section className="py-16 bg-gradient-to-b from-blue-50 to-blue-100">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-primary-blue mb-4">
            Calendário de Atividades
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Na Abrace Brasil, oferecemos uma variedade de programas projetados
            para imergir as crianças na língua e cultura brasileiras. Nossos
            instrutores experientes criam um ambiente acolhedor que estimula o
            aprendizado e a criatividade.
          </p>
        </div>

        <div className="max-w-6xl mx-auto mb-8">
          {loading ? (
            <div className="h-96 bg-gray-200 animate-pulse rounded-lg"></div>
          ) : (
            <EventCalendar
              events={events.map(event => ({
                id: event.id,
                title: event.title,
                start: new Date(`${event.startDate}T${event.startTime}`),
                end: new Date(`${event.endDate}T${event.endTime}`),
                resource: event,
              }))}
              onSelectEvent={handleEventSelect}
              onSelectSlot={handleSlotSelect}
            />
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button size="lg" asChild>
            <a href="/volunteer">Voluntarie-se</a>
          </Button>
          <Button size="lg" asChild>
            <a href="/register">Matrículas</a>
          </Button>
          <Button size="lg" asChild>
            <a href="/donate">Fazer Doação</a>
          </Button>
        </div>
      </div>
    </section>
  );
}
