import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { getYear } from "date-fns";

export function Footer() {
  return (
    <footer className="bg-blue-100 text-blue-900">
      <div className="container mx-auto px-4 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Logo and Description */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                <span className="text-primary-blue font-bold text-sm">AB</span>
              </div>
              <span className="font-bold text-xl">Abrace Brasil</span>
            </div>
            <p className="text-blue-900 text-sm leading-relaxed">
              Preservando a cultura brasileira através da educação e comunidade.
            </p>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Links Rápidos</h3>
            <nav className="flex flex-col space-y-2">
              <Link
                href="/sobre"
                className="text-blue-900 hover:text-blue-600 transition-colors text-sm"
              >
                Sobre Nós
              </Link>
              <Link
                href="/programas"
                className="text-blue-900 hover:text-blue-600 transition-colors text-sm"
              >
                Programas
              </Link>
              <Link
                href="/events"
                className="text-blue-900 hover:text-blue-600 transition-colors text-sm"
              >
                Eventos
              </Link>
              <Link
                href="/volunteer"
                className="text-blue-900 hover:text-blue-600 transition-colors text-sm"
              >
                Voluntário
              </Link>
            </nav>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Contato</h3>
            <div className="space-y-2 text-sm text-blue-900">
              <p>Washington DC, USA</p>
              <p><EMAIL></p>
              <p>(202) 555-0123</p>
            </div>
          </div>

          {/* Social Media */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Redes Sociais</h3>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                size="sm"
                className="border-white text-blue-900 hover:bg-white hover:text-primary-blue"
              >
                Facebook
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="border-white text-blue-900 hover:bg-white hover:text-primary-blue"
              >
                Instagram
              </Button>
            </div>
          </div>
        </div>
      </div>
      <div className="bg-blue-200 mt-8 py-2 text-center">
        <p className="text-blue-900 text-sm">
          © {getYear(new Date())} Abrace Brasil. Todos os direitos reservados.
        </p>
      </div>
    </footer>
  );
}
