"use client";

import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { AnimatedServicesCarousel } from "@/components/ui/animated-services-carousel";
import { strapiService } from "@/lib/strapi";
import { StrapiGeneralContent } from "@/types/strapi";
import Image from "next/image";
import { useEffect, useState } from "react";

interface FeaturedItem {
  id: number;
  title: string;
  slug: string;
  type: 'program' | 'article' | 'page' | 'calendar';
  content: any[];
  thumbnail: any;
}

export function HeroSection() {
  const t = useTranslations();
  const [generalContent, setGeneralContent] = useState<StrapiGeneralContent | null>(null);
  const [featuredItems, setFeaturedItems] = useState<FeaturedItem[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchData = async () => {
    try {
      const [general, featured] = await Promise.all([
        strapiService.getGeneralContent(),
        strapiService.getFeaturedItems(),
      ]);

      return { general, featured }
      console.log('featured', featured)
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };


  useEffect(() => {   
    fetchData().then((result) => {
      if (result) {
        setGeneralContent(result.general);
        setFeaturedItems(result.featured);
      }
    });
  }, []);


  // Transform featured items for the carousel
  const services = featuredItems.map((item, index) => {
    // Extract description from content (first paragraph)
    const firstParagraph = item.content.find((c: any) => c.type === 'paragraph');
    const description = firstParagraph?.children?.map((c: any) => c.text).join('') || '';

    return {
      id: item.id,
      title: item.title,
      description: description.substring(0, 100) + (description.length > 100 ? '...' : ''),
      details: description,
      thumbnail: item.thumbnail,
      color: "bg-white",
      href: `/${item.type}s/${item.slug}`,
    };
  });

  console.log('services', services)

  return (
    <section className="bg-gradient-to-br from-white to-gray-50 py-16 lg:py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 ">
          {/* Left Content - Logo Area (33%) */}
          <div className="lg:col-span-1 text-center lg:text-left space-y-6">
            {/* Logo */}
            <div className="flex flex-col items-center lg:items-start space-y-4">
              {loading ? (
                <div className="w-[80%] h-32 bg-gray-200 animate-pulse rounded"></div>
              ) : generalContent?.logo ? (
                <Image
                  src={generalContent.logo.url}
                  alt={generalContent.logo.alternativeText || "Abrace Brasil Logo"}
                  width={generalContent.logo.width}
                  height={generalContent.logo.height}
                  className="w-[80%] h-auto"
                  priority
                />
              ) : (
                <img
                  src="/images/abrace-logo.jpg"
                  alt="Abrace Brasil Logo"
                  className="w-[80%] h-auto"
                />
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              {generalContent?.enrollmentlink && (
                <Button
                  size="lg"
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                  asChild
                >
                  <a href={generalContent.enrollmentlink} target="_blank" rel="noopener noreferrer">
                    Inscreva-se
                  </a>
                </Button>
              )}
              <Button
                size="lg"
                className="bg-blue-600 hover:bg-blue-700 text-white"
                asChild
              >
                <a href="/about">
                  Saiba mais
                </a>
              </Button>
              {generalContent?.volunteerlink && (
                <Button
                  size="lg"
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                  asChild
                >
                  <a href={generalContent.volunteerlink} target="_blank" rel="noopener noreferrer">
                    Voluntarie-se
                  </a>
                </Button>
              )}
            </div>
          </div>

          {/* Right Content - Services Carousel (66%) */}
          <div className="lg:col-span-2">
            <div className="space-y-8">
              {/* Carousel */}
              {loading ? (
                <div className="h-64 bg-gray-200 animate-pulse rounded-lg"></div>
              ) : featuredItems.length > 0 ? (
                <AnimatedServicesCarousel
                  services={services}
                  autoPlay={true}
                  autoPlayDelay={3000}
                />
              ) : (
                <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                  <p className="text-gray-500">Nenhum item em destaque encontrado</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
