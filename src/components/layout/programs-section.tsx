"use client";

import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { strapiService } from "@/lib/strapi";
import { StrapiService } from "@/types/strapi";
import Image from "next/image";
import { useEffect, useState } from "react";
import Link from "next/link";

export function ProgramsSection() {
  const [programs, setPrograms] = useState<StrapiService[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPrograms = async () => {
      try {
        const data = await strapiService.getPrograms();
        setPrograms(data);
      } catch (error) {
        console.error('Error fetching programs:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPrograms();
  }, []);

  const getColorClass = (index: number) => {
    const colors = ["bg-orange-100", "bg-blue-100", "bg-green-100", "bg-yellow-100"];
    return colors[index % colors.length];
  };

  const getIcon = (index: number) => {
    const icons = ["👶", "🎵", "👨‍🎓", "🎉"];
    return icons[index % icons.length];
  };

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Descubra Nossos Programas de Cultura e Língua
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Oferecemos uma variedade de programas educacionais para todas as
            idades, desde crianças pequenas até adultos, todos focados em
            preservar e celebrar a rica cultura brasileira.
          </p>
        </div>

        {loading ? (
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow duration-300">
                <CardHeader className="p-0">
                  <div className="h-48 bg-gray-200 animate-pulse rounded-t-lg"></div>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="h-6 bg-gray-200 animate-pulse rounded mb-2"></div>
                  <div className="h-4 bg-gray-200 animate-pulse rounded mb-3"></div>
                  <div className="space-y-2">
                    <div className="h-3 bg-gray-200 animate-pulse rounded"></div>
                    <div className="h-3 bg-gray-200 animate-pulse rounded"></div>
                    <div className="h-3 bg-gray-200 animate-pulse rounded w-3/4"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : programs.length > 0 ? (
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {programs.map((program, index) => {
              // Extract first paragraph for description
              const firstParagraph = program.content.find(c => c.type === 'paragraph');
              const description = firstParagraph?.children?.map(c => c.text).join('') || '';
              
              return (
                <Card
                  key={program.id}
                  className="hover:shadow-lg transition-shadow duration-300 py-0"
                >
                  <CardHeader className="p-0">
                    <div
                      className={`h-48 ${getColorClass(index)} rounded-t-lg flex items-center justify-center`}
                    >
                      {program.thumbnail ? (
                        <Image
                          src={program.thumbnail.url}
                          alt={program.thumbnail.alternativeText || program.title}
                          width={program.thumbnail.width}
                          height={program.thumbnail.height}
                          className="w-full h-full object-cover rounded-t-lg"
                        />
                      ) : (
                        <div className="text-6xl opacity-50">
                          {getIcon(index)}
                        </div>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="p-6 flex flex-col justify-between h-full">
                    <div>
                      <CardTitle className="text-xl font-bold text-gray-900 mb-2">
                        {program.title}
                      </CardTitle>
                    </div>
                    <div className="mt-4">
                      <Button asChild className="w-full">
                        <Link href={`/programs/${program.slug}`}>
                          Saiba mais
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500">Nenhum programa encontrado</p>
          </div>
        )}
      </div>
    </section>
  );
}
