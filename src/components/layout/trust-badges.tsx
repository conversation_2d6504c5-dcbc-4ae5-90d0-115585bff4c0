"use client";

import Image from "next/image";
import { useEffect, useState } from "react";
import { strapiService } from "@/lib/strapi";
import { StrapiGeneralContent } from "@/types/strapi";

export function TrustBadges() {
  const [generalContent, setGeneralContent] = useState<StrapiGeneralContent | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const content = await strapiService.getGeneralContent();
        setGeneralContent(content);
      } catch (error) {
        console.error('Error fetching general content:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Fallback awards if no data from API
  const fallbackAwards = [
    {
      id: 1,
      src: "/images/award-1.jpg",
      alt: "GreatNonprofits 2019 Top-Rated Nonprofit",
      width: 140,
      height: 90,
    },
    {
      id: 2,
      src: "/images/award-2.png",
      alt: "2024 Top-Rated Nonprofit Award",
      width: 90,
      height: 90,
    },
    {
      id: 3,
      src: "/images/award-3.png",
      alt: "2024 Top-Rated Nonprofit Award",
      width: 90,
      height: 90,
    },
  ];

  const awards = generalContent?.badges && generalContent.badges.length > 0 
    ? generalContent.badges.map((badge, index) => ({
        id: badge.id,
        src: `/images/award-${index + 1}.jpg`, // Using local images for now
        alt: badge.title,
        width: 140,
        height: 90,
      }))
    : fallbackAwards;

  return (
    <section className="bg-blue-600 py-12">
      <div className="container mx-auto px-4">
        <div className="flex flex-col sm:flex-row items-center justify-center gap-6 sm:gap-8 md:gap-12 lg:gap-16">
          {awards.map((award) => (
            <div
              key={award.id}
              className="flex items-center justify-center transition-transform duration-200 hover:scale-105"
            >
              <Image
                src={award.src}
                alt={award.alt}
                width={award.width}
                height={award.height}
                className="object-contain w-auto h-12 sm:h-14 md:h-16 lg:h-22"
                priority
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
