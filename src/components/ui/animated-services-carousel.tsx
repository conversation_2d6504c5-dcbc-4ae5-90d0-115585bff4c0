"use client";

import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation } from "swiper/modules";
import { Card, CardTitle } from "@/components/ui/card";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/autoplay";

interface Service {
  id: number;
  title: string;
  description: string;
  details: string;
  thumbnail: any;
  color: string;
  href: string;
}

interface AnimatedServicesCarouselProps {
  services: Service[];
  autoPlay?: boolean;
  autoPlayDelay?: number;
}

export function AnimatedServicesCarousel({
  services,
  autoPlay = true,
  autoPlayDelay = 3000,
}: AnimatedServicesCarouselProps) {
  if (services.length === 0) return null;

  return (
    <div className="relative w-full">
      {/* Fixed square container */}
      <div
        className="w-full h-auto md:h-120 lg:h-80 xl:h-96 2xl:h-120"
        style={{ aspectRatio: "1/1" }}
      >
        <Swiper
          modules={[Autoplay, Navigation]}
          spaceBetween={16}
          slidesPerView={3}
          autoplay={
            autoPlay
              ? {
                  delay: autoPlayDelay,
                  disableOnInteraction: false,
                }
              : false
          }
          navigation={{
            nextEl: ".swiper-button-next-custom",
            prevEl: ".swiper-button-prev-custom",
          }}
          loop={true}
          breakpoints={{
            320: {
              slidesPerView: 1,
              spaceBetween: 8,
            },
            768: {
              slidesPerView: 2,
              spaceBetween: 12,
            },
            1024: {
              slidesPerView: 3,
              spaceBetween: 16,
            },
          }}
          className="w-full h-full"
        >
          {services.map((service) => (
            <SwiperSlide key={service.id}>
              <div
                style={{ width: "100%", aspectRatio: "1/1" }}
                className="pt-2 px-2"
              >
                <Link href={service.href} className="block">
                  <Card
                    className="overflow-hidden bg-white border-transparent shadow-lg hover:shadow-none hover:border hover:border-blue-500 transition-all duration-300 transform hover:scale-102 w-full flex flex-col pt-0 cursor-pointer"
                    style={{ aspectRatio: "1/1" }}
                  >
                    {/* Image area - 66% of height */}
                    <div
                      className="flex-none bg-gradient-to-br from-gray-50 to-gray-100 rounded-t-lg flex items-center justify-center relative overflow-hidden"
                      style={{ height: "66%" }}
                    >
                      {service.thumbnail ? (
                        <Image
                          src={service.thumbnail.url}
                          alt={service.thumbnail.alternativeText || service.title}
                          width={service.thumbnail.width}
                          height={service.thumbnail.height}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="text-4xl opacity-50">📄</div>
                      )}
                      {/* Decorative elements */}
                      <div className="absolute top-2 right-2 w-2 h-2 bg-blue-200 rounded-full"></div>
                      <div className="absolute bottom-2 left-2 w-1.5 h-1.5 bg-green-200 rounded-full"></div>
                    </div>

                    {/* Title area - 34% of height */}
                    <div
                      className="flex-none flex items-center justify-center p-3"
                      style={{ height: "34%" }}
                    >
                      <CardTitle className="text-center text-xs font-bold text-gray-900 leading-tight">
                        {service.title}
                      </CardTitle>
                    </div>
                  </Card>
                </Link>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>

      {/* Custom CSS to force square slides */}
      <style jsx global>{`
        .swiper-slide {
          height: auto !important;
          aspect-ratio: 1/1 !important;
        }

        .swiper-wrapper {
          align-items: flex-start !important;
        }
      `}</style>

      {/* Custom Navigation Arrows */}
      <button
        className="swiper-button-prev-custom absolute left-2 top-1/3 transform -translate-y-1/3 bg-white/90 hover:bg-white rounded-full p-2 shadow-lg transition-all duration-200 hover:scale-110 z-10"
        aria-label="Serviços anteriores"
      >
        <ChevronLeft size={16} className="text-gray-700" />
      </button>

      <button
        className="swiper-button-next-custom absolute right-2 top-1/3 transform -translate-y-1/3 bg-white/90 hover:bg-white rounded-full p-2 shadow-lg transition-all duration-200 hover:scale-110 z-10"
        aria-label="Próximos serviços"
      >
        <ChevronRight size={16} className="text-gray-700" />
      </button>
    </div>
  );
}
