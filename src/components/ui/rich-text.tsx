import { StrapiRichText } from "@/types/strapi";

interface RichTextProps {
  content: StrapiRichText[];
  className?: string;
}

export function RichText({ content, className = "" }: RichTextProps) {
  const renderRichText = (item: StrapiRichText, index: number) => {
    const key = `${item.type}-${index}`;
    
    if (item.type === "heading") {
      const HeadingTag = `h${item.level || 3}` as keyof JSX.IntrinsicElements;
      return (
        <HeadingTag key={key} className="font-bold mb-4">
          {item.children.map((child, childIndex) => (
            <span key={childIndex} className={child.bold ? "font-bold" : ""}>
              {child.text}
            </span>
          ))}
        </HeadingTag>
      );
    }
    
    if (item.type === "paragraph") {
      const text = item.children.map(child => child.text).join("");
      
      // Skip empty paragraphs
      if (!text.trim()) {
        return <div key={key} className="mb-2" />;
      }
      
      return (
        <p key={key} className="mb-4">
          {item.children.map((child, childIndex) => (
            <span key={childIndex} className={child.bold ? "font-bold" : ""}>
              {child.text}
            </span>
          ))}
        </p>
      );
    }
    
    return null;
  };

  return (
    <div className={`prose prose-lg max-w-none ${className}`}>
      {content.map((item, index) => renderRichText(item, index))}
    </div>
  );
}
