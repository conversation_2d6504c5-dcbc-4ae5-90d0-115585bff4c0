// Shared configuration for locales across the application
export const locales = ["pt-BR", "en"] as const;
export const defaultLocale = "pt-BR" as const;

export type Locale = (typeof locales)[number];

// Locale display names for UI
export const localeNames: Record<Locale, string> = {
  "pt-BR": "Português (Brasil)",
  en: "English",
};

// Locale flags for UI
export const localeFlags: Record<Locale, string> = {
  "pt-BR": "🇧🇷",
  en: "🇺🇸",
};
