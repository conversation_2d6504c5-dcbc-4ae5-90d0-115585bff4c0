import {
  StrapiEvent,
  StrapiService as StrapiServiceType,
  StrapiArticle,
  StrapiPage,
  StrapiGeneralContent,
  MenuStructure,
  RegistrationFormData,
  DateFilter,
} from "@/types/strapi";

class StrapiService {
  private baseUrl: string;
  private apiToken: string;

  constructor() {
    this.baseUrl =
      process.env.NEXT_PUBLIC_STRAPI_URL || "http://localhost:1337";
    this.apiToken = process.env.STRAPI_API_TOKEN || "";
  }

  private async fetchAPI(endpoint: string, options: RequestInit = {}) {
    const url = `${this.baseUrl}/api${endpoint}`;
    const headers = {
      "Content-Type": "application/json",
      ...(this.apiToken && { Authorization: `Bearer ${this.apiToken}` }),
      ...options.headers,
    };

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Strapi API Error:", error);
      throw error;
    }
  }

  async getMenuStructure(locale: string): Promise<MenuStructure> {
    const response = await this.fetchAPI(`/menu-structure?locale=${locale}&populate=*`);
    return response.data;
  }

  async getEvents(locale: string): Promise<StrapiEvent[]> {
    const response = await this.fetchAPI(`/events?locale=${locale}&populate=*`);
    return response.data;
  }

  async getServices(locale: string): Promise<StrapiServiceType[]> {
    const response = await this.fetchAPI(`/services?locale=${locale}&populate=*`);
    return response.data;
  }

  async getBlogArticles(
    locale: string,
    type: "blog" | "page"
  ): Promise<StrapiArticle[]> {
    const response = await this.fetchAPI(`/articles?locale=${locale}&filters[type][$eq]=${type}&populate=*`);
    return response.data;
  }

  async submitRegistration(data: RegistrationFormData): Promise<void> {
    await this.fetchAPI("/registrations", {
      method: "POST",
      body: JSON.stringify({ data }),
    });
  }

  async exportRegistrations(filters: DateFilter): Promise<Blob> {
    const queryParams = new URLSearchParams();
    if (filters.startDate) queryParams.append("filters[startDate][$gte]", filters.startDate);
    if (filters.endDate) queryParams.append("filters[endDate][$lte]", filters.endDate);
    
    const response = await this.fetchAPI(`/registrations/export?${queryParams.toString()}`);
    return response.blob();
  }

  async getArticleBySlug(
    slug: string,
    locale: string
  ): Promise<StrapiArticle | null> {
    try {
      const response = await this.fetchAPI(`/articles?filters[slug][$eq]=${slug}&locale=${locale}&populate=*`);
      return response.data[0] || null;
    } catch (error) {
      console.error("Error fetching article by slug:", error);
      return null;
    }
  }

  async getDonationLink(): Promise<string | null> {
    try {
      const response = await this.fetchAPI("/donation-link?populate=*");
      return response.data?.url || null;
    } catch (error) {
      console.error("Error fetching donation link:", error);
      return null;
    }
  }

  // New API methods for the home page
  async getGeneralContent(): Promise<StrapiGeneralContent> {
    const response = await this.fetchAPI("/general-content?populate[badges][populate][image]=*");
    return response.data;
  }

  async getPrograms(): Promise<StrapiServiceType[]> {
    try {
      const response = await this.fetchAPI("/programs?populate=*");
      console.log('getPrograms response:', response);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching programs:', error);
      return [];
    }
  }

  async getArticles(): Promise<StrapiArticle[]> {
    try {
      const response = await this.fetchAPI("/articles?populate=*");
      console.log('getArticles response:', response);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching articles:', error);
      return [];
    }
  }

  async getPages(): Promise<StrapiPage[]> {
    try {
      const response = await this.fetchAPI("/pages?populate=*");
      console.log('getPages response:', response);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching pages:', error);
      return [];
    }
  }

  async getCalendars(): Promise<StrapiEvent[]> {
    try {
      const response = await this.fetchAPI("/calendars?populate=*");
      console.log('getCalendars response:', response);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching calendars:', error);
      return [];
    }
  }

  async getFeaturedItems() {
    try {
      const [programs, articles, pages, calendars] = await Promise.all([
        this.getPrograms(),
        this.getArticles(),
        this.getPages(),
        this.getCalendars(),
      ]);

      console.log('getFeaturedItems - programs:', programs);
      console.log('getFeaturedItems - articles:', articles);
      console.log('getFeaturedItems - pages:', pages);
      console.log('getFeaturedItems - calendars:', calendars);

      const featuredPrograms = programs.filter(item => item.featured).map(item => ({ ...item, type: 'program' as const }));
      const featuredArticles = articles.filter(item => item.featured).map(item => ({ ...item, type: 'article' as const }));
      const featuredPages = pages.filter(item => item.featured).map(item => ({ ...item, type: 'page' as const }));
      const featuredCalendars = calendars.filter(item => item.featured).map(item => ({ ...item, type: 'calendar' as const }));

      console.log('featuredPrograms:', featuredPrograms);
      console.log('featuredArticles:', featuredArticles);
      console.log('featuredPages:', featuredPages);
      console.log('featuredCalendars:', featuredCalendars);

      const featuredItems = [
        ...featuredPrograms,
        ...featuredArticles,
        ...featuredPages,
        ...featuredCalendars,
      ];

      console.log('final featuredItems:', featuredItems);
      return featuredItems;
    } catch (error) {
      console.error('Error in getFeaturedItems:', error);
      return [];
    }
  }
}

export const strapiService = new StrapiService();
