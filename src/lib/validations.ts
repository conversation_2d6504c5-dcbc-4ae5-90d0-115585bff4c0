import { z } from "zod";

export const registrationSchema = z.object({
  parentName: z
    .string()
    .min(2, "Nome do responsável deve ter pelo menos 2 caracteres"),
  parentEmail: z.string().email("Email inválido"),
  parentPhone: z.string().min(10, "Telefone deve ter pelo menos 10 dígitos"),
  childName: z
    .string()
    .min(2, "Nome da criança deve ter pelo menos 2 caracteres"),
  childAge: z.number().min(1).max(18, "Idade deve estar entre 1 e 18 anos"),
  childBirthDate: z.string().min(1, "Data de nascimento é obrigatória"),
  emergencyContact: z
    .string()
    .min(10, "Contato de emergência deve ter pelo menos 10 dígitos"),
  medicalInfo: z.string().optional(),
  selectedPrograms: z
    .array(z.string())
    .min(1, "Selecione pelo menos um programa"),
});

export type RegistrationFormData = z.infer<typeof registrationSchema>;
