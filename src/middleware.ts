import createMiddleware from "next-intl/middleware";
import { locales, defaultLocale } from "./lib/config";

export default createMiddleware({
  // A list of all locales that are supported
  locales,

  // Used when no locale matches - always default to Brazilian Portuguese
  defaultLocale,

  // Disable locale detection based on browser preferences
  // This ensures Brazilian users in the USA see Portuguese by default
  localeDetection: false,
});

export const config = {
  // Match only internationalized pathnames
  matcher: ["/", "/(pt-BR|en)/:path*"],
};
