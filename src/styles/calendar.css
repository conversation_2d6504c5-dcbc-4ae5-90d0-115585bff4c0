/* React Big Calendar Custom Styles */
@import 'react-big-calendar/lib/css/react-big-calendar.css';

.rbc-calendar {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  border: none;
}

.rbc-header {
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  padding: 12px 8px;
  font-weight: 600;
  color: #475569;
  text-align: center;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.rbc-month-view {
  border: none;
}

.rbc-date-cell {
  padding: 8px;
  text-align: center;
  border-right: 1px solid #e2e8f0;
  border-bottom: 1px solid #e2e8f0;
  min-height: 80px;
  background: white;
  position: relative;
}

.rbc-date-cell:hover {
  background: #f1f5f9;
}

.rbc-off-range-bg {
  background: #f8fafc !important;
  color: #94a3b8;
}

.rbc-today {
  background: #dbeafe !important;
  border: 2px solid #3b82f6;
}

.rbc-event {
  background: #fbbf24 !important;
  border: none !important;
  border-radius: 6px;
  color: #92400e !important;
  font-weight: 500;
  padding: 2px 6px;
  margin: 1px;
  font-size: 11px;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
}

.rbc-event:hover {
  background: #f59e0b !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

.rbc-event.rbc-selected {
  background: #d97706 !important;
}

.rbc-month-row {
  border-bottom: 1px solid #e2e8f0;
}

.rbc-month-row:last-child {
  border-bottom: none;
}

.rbc-day-bg {
  border-right: 1px solid #e2e8f0;
}

.rbc-day-bg:last-child {
  border-right: none;
}

.rbc-date-cell a {
  color: #3b82f6 !important;
  font-weight: 500;
  text-decoration: none;
  font-size: 14px;
}

.rbc-date-cell a:hover {
  color: #1d4ed8 !important;
}

.rbc-off-range .rbc-date-cell a {
  color: #94a3b8 !important;
}

.rbc-toolbar {
  display: none !important;
}

.rbc-month-header {
  border-bottom: 2px solid #e2e8f0;
}

.rbc-header + .rbc-header {
  border-left: 1px solid #e2e8f0;
}

.rbc-date-cell + .rbc-date-cell {
  border-left: none;
}

.rbc-row-content {
  z-index: 4;
}

.rbc-addons-dnd .rbc-addons-dnd-row-body {
  height: auto;
}

.rbc-show-more {
  background: #3b82f6;
  color: white;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 10px;
  font-weight: 500;
  margin: 1px;
  cursor: pointer;
}

.rbc-show-more:hover {
  background: #1d4ed8;
}

/* Custom responsive adjustments */
@media (max-width: 768px) {
  .rbc-date-cell {
    min-height: 60px;
    padding: 4px;
  }
  
  .rbc-header {
    padding: 8px 4px;
    font-size: 12px;
  }
  
  .rbc-event {
    font-size: 10px;
    padding: 1px 4px;
  }
}