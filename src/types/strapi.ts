// Strapi Content Types
export interface StrapiEvent {
  id: number;
  documentId: string;
  title: string;
  slug: string;
  featured: boolean;
  startDate: string;
  startTime: string;
  endDate: string;
  endTime: string;
  location?: string;
  category?: string;
  content: StrapiRichText[];
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  thumbnail: StrapiMedia | null;
  banner: StrapiMedia | null;
  linkbutton: StrapiLinkButton[];
}

export interface StrapiService {
  id: number;
  documentId: string;
  title: string;
  slug: string;
  featured: boolean;
  content: StrapiRichText[];
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  thumbnail: StrapiMedia | null;
  banner: StrapiMedia | null;
  linkbutton: StrapiLinkButton[];
}

export interface StrapiArticle {
  id: number;
  documentId: string;
  title: string;
  slug: string;
  featured: boolean;
  content: StrapiRichText[];
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  thumbnail: StrapiMedia | null;
  banner: StrapiMedia | null;
}

export interface StrapiPage {
  id: number;
  documentId: string;
  title: string;
  slug: string;
  featured: boolean;
  content: StrapiRichText[];
  category: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  thumbnail: StrapiMedia | null;
  banner: StrapiMedia | null;
  linkbutton: StrapiLinkButton[];
}

export interface StrapiGeneralContent {
  id: number;
  documentId: string;
  phone: string | null;
  email: string;
  address: string;
  facebook: string | null;
  instagram: string | null;
  linkedin: string | null;
  youtube: string | null;
  tiktok: string | null;
  whatsapp: string | null;
  donationlink: string;
  volunteerlink: string;
  enrollmentlink: string | null;
  careerlink: string | null;
  logo: StrapiMedia;
  logodark: StrapiMedia | null;
  logomobile: StrapiMedia | null;
  logomobiledark: StrapiMedia | null;
  badges: StrapiBadge[];
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
}

export interface StrapiBadge {
  id: number;
  title: string;
}

export interface StrapiLinkButton {
  id: number;
  link: string;
  label: string;
  position: string;
}

export interface StrapiRichText {
  type: "paragraph" | "heading";
  level?: number;
  children: StrapiRichTextChild[];
}

export interface StrapiRichTextChild {
  text: string;
  type: "text";
  bold?: boolean;
}

export interface StrapiRegistration {
  id: number;
  parentName: string;
  parentEmail: string;
  parentPhone: string;
  childName: string;
  childAge: number;
  childBirthDate: string;
  emergencyContact: string;
  medicalInfo?: string;
  selectedPrograms: string[];
  submittedAt: string;
  status: "pending" | "approved" | "rejected";
}

export interface StrapiCategory {
  id: number;
  name: string;
  slug: string;
  description?: string;
}

export interface StrapiMedia {
  id: number;
  documentId: string;
  name: string;
  alternativeText: string | null;
  caption: string | null;
  width: number;
  height: number;
  formats: {
    thumbnail?: StrapiMediaFormat;
    small?: StrapiMediaFormat;
    medium?: StrapiMediaFormat;
  };
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl: string | null;
  provider: string;
  provider_metadata: any;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
}

export interface StrapiMediaFormat {
  ext: string;
  url: string;
  hash: string;
  mime: string;
  name: string;
  path: string | null;
  size: number;
  width: number;
  height: number;
  sizeInBytes: number;
}

// Menu Structure
export interface MenuStructure {
  mainMenu: MenuItem[];
}

export interface MenuItem {
  id: number;
  label: string;
  url: string;
  target: "_self" | "_blank";
  children?: MenuItem[];
  order: number;
}

// Form Data Types
export interface RegistrationFormData {
  parentName: string;
  parentEmail: string;
  parentPhone: string;
  childName: string;
  childAge: number;
  childBirthDate: string;
  emergencyContact: string;
  medicalInfo?: string;
  selectedPrograms: string[];
}

export interface DateFilter {
  startDate?: string;
  endDate?: string;
  exactDate?: string;
}

// Component Props
export interface EventCalendarProps {
  events: StrapiEvent[];
  locale: string;
  onEventSelect: (event: StrapiEvent) => void;
}

export interface ServiceCardProps {
  service: StrapiService;
  category: StrapiCategory;
  locale: string;
}

export interface BlogCardProps {
  article: StrapiArticle;
  locale: string;
  showExcerpt?: boolean;
}

export interface ExportPanelProps {
  onExport: (filters: DateFilter) => void;
  isEditor: boolean;
}
